const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');

const app = express();
const server = http.createServer(app);

// Configure CORS for Socket.IO
const io = socketIo(server, {
  cors: {
    origin: ["http://localhost:8080", "http://localhost:8081"], // Your frontend URLs
    methods: ["GET", "POST"],
    credentials: true
  }
});

app.use(cors());
app.use(express.json());

// Store active auctions and their current state
const auctions = new Map();

// Time synchronization state
let serverStartTime = Date.now();
const timeSyncClients = new Map(); // Store client time offsets

// Initialize some sample auction data
auctions.set('fe1', {
  id: 'fe1',
  currentBid: 185,
  bidCount: 23,
  bids: []
});

auctions.set('1', {
  id: '1',
  currentBid: 12750,
  bidCount: 15,
  bids: []
});

// Add new products from the provided list
auctions.set('taylor-swift', {
  id: 'taylor-swift',
  currentBid: 850,
  bidCount: 42,
  bids: []
});

auctions.set('disney-tickets', {
  id: 'disney-tickets',
  currentBid: 320,
  bidCount: 18,
  bids: []
});

auctions.set('dg-sunglasses', {
  id: 'dg-sunglasses',
  currentBid: 450,
  bidCount: 12,
  bids: []
});

auctions.set('labubu', {
  id: 'labubu',
  currentBid: 125,
  bidCount: 28,
  bids: []
});

auctions.set('pokemon-cards', {
  id: 'pokemon-cards',
  currentBid: 2800,
  bidCount: 35,
  bids: []
});

auctions.set('first-class-upgrade', {
  id: 'first-class-upgrade',
  currentBid: 1200,
  bidCount: 22,
  bids: []
});

auctions.set('beats-earphones', {
  id: 'beats-earphones',
  currentBid: 180,
  bidCount: 15,
  bids: []
});

auctions.set('bulgari-wonders', {
  id: 'bulgari-wonders',
  currentBid: 3500,
  bidCount: 8,
  bids: []
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`User connected: ${socket.id}`);

  // Handle time synchronization request
  socket.on('time_sync_request', (clientTime) => {
    const serverTime = Date.now();
    socket.emit('time_sync_response', {
      clientTime,
      serverTime,
      roundTripTime: 0 // Will be calculated on client side
    });
  });

  // Handle auction subscription
  socket.on('subscribe_auction', (auctionId) => {
    console.log(`User ${socket.id} subscribed to auction ${auctionId}`);
    socket.join(auctionId);

    // Send current auction state to the newly connected user
    const auction = auctions.get(auctionId);
    if (auction) {
      socket.emit('auction_update', {
        auctionId,
        currentBid: auction.currentBid,
        bidCount: auction.bidCount,
        serverTime: Date.now() // Include server time for sync
      });
    }
  });

  // Handle auction unsubscription
  socket.on('unsubscribe_auction', (auctionId) => {
    console.log(`User ${socket.id} unsubscribed from auction ${auctionId}`);
    socket.leave(auctionId);
  });

  // Handle new bid placement
  socket.on('place_bid', (data) => {
    const { auctionId, username, price } = data;
    console.log(`New bid from ${username}: $${price} for auction ${auctionId}`);

    // Get auction data
    const auction = auctions.get(auctionId);
    if (!auction) {
      socket.emit('bid_error', { message: 'Auction not found' });
      return;
    }

    // Validate bid amount
    if (price <= auction.currentBid) {
      socket.emit('bid_error', { 
        message: `Bid must be higher than current bid of $${auction.currentBid}` 
      });
      return;
    }

    // Create new bid object with server time
    const serverTime = Date.now();
    const newBid = {
      id: `bid-${serverTime}-${Math.random().toString(36).substring(2, 11)}`,
      auctionId,
      amount: price,
      bidder: {
        name: username,
        avatar: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face"
      },
      timestamp: new Date(serverTime),
      serverTime: serverTime
    };

    // Update auction state
    auction.currentBid = price;
    auction.bidCount += 1;
    auction.bids.unshift(newBid);

    // Keep only last 50 bids to prevent memory issues
    if (auction.bids.length > 50) {
      auction.bids = auction.bids.slice(0, 50);
    }

    // Broadcast the new bid to all users in the auction room
    io.to(auctionId).emit('new_bid', {
      bid: newBid,
      serverTime: serverTime
    });

    // Also broadcast auction update
    io.to(auctionId).emit('auction_update', {
      auctionId,
      currentBid: auction.currentBid,
      bidCount: auction.bidCount,
      serverTime: serverTime
    });

    console.log(`Bid processed: ${username} bid $${price} for auction ${auctionId}`);
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log(`User disconnected: ${socket.id}`);
  });
});

// Time synchronization endpoint
app.get('/api/time', (req, res) => {
  res.json({
    serverTime: Date.now(),
    timestamp: new Date().toISOString()
  });
});

// Basic health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    activeConnections: io.engine.clientsCount,
    activeAuctions: auctions.size
  });
});

// Get auction data endpoint
app.get('/auction/:id', (req, res) => {
  const auction = auctions.get(req.params.id);
  if (!auction) {
    return res.status(404).json({ error: 'Auction not found' });
  }
  res.json(auction);
});

const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  console.log(`🚀 Live Auction Server running on port ${PORT}`);
  console.log(`📡 Socket.IO server ready for connections`);
  console.log(`🔗 Frontend should connect to: http://localhost:${PORT}`);
});
