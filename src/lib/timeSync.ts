/**
 * Time Synchronization Service
 * Handles synchronization between client and server time to ensure consistent timestamps
 * and countdown timers across all users.
 */

export interface TimeSyncData {
  clientTime: number;
  serverTime: number;
  roundTripTime: number;
}

export interface TimeSyncStatus {
  synchronized: boolean;
  offset: number; // Server time - client time
  lastSync: number;
  error?: string;
}

class TimeSyncService {
  private offset: number = 0;
  private lastSyncTime: number = 0;
  private syncInterval: NodeJS.Timeout | null = null;
  private readonly SYNC_INTERVAL = 30000; // Sync every 30 seconds
  private readonly MAX_SYNC_AGE = 60000; // Consider sync stale after 1 minute
  private listeners: ((status: TimeSyncStatus) => void)[] = [];

  constructor() {
    this.startPeriodicSync();
  }

  /**
   * Get the current server time based on synchronized offset
   */
  getServerTime(): number {
    return Date.now() + this.offset;
  }

  /**
   * Convert a client timestamp to server time
   */
  clientToServerTime(clientTime: number): number {
    return clientTime + this.offset;
  }

  /**
   * Convert a server timestamp to client time
   */
  serverToClientTime(serverTime: number): number {
    return serverTime - this.offset;
  }

  /**
   * Check if time synchronization is current
   */
  isSynchronized(): boolean {
    const age = Date.now() - this.lastSyncTime;
    return age < this.MAX_SYNC_AGE && this.lastSyncTime > 0;
  }

  /**
   * Get current synchronization status
   */
  getStatus(): TimeSyncStatus {
    return {
      synchronized: this.isSynchronized(),
      offset: this.offset,
      lastSync: this.lastSyncTime,
    };
  }

  /**
   * Perform time synchronization with server via HTTP
   */
  async syncWithServer(): Promise<TimeSyncStatus> {
    try {
      const clientSendTime = Date.now();
      
      const response = await fetch('/api/time', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const clientReceiveTime = Date.now();
      const data = await response.json();
      const serverTime = data.serverTime;

      // Calculate round trip time and estimated server time
      const roundTripTime = clientReceiveTime - clientSendTime;
      const estimatedServerTime = serverTime + (roundTripTime / 2);
      
      // Update offset
      this.offset = estimatedServerTime - clientReceiveTime;
      this.lastSyncTime = Date.now();

      const status = this.getStatus();
      this.notifyListeners(status);

      console.log(`Time sync completed. Offset: ${this.offset}ms, RTT: ${roundTripTime}ms`);
      return status;
    } catch (error) {
      const status: TimeSyncStatus = {
        synchronized: false,
        offset: this.offset,
        lastSync: this.lastSyncTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      
      this.notifyListeners(status);
      console.error('Time sync failed:', error);
      return status;
    }
  }

  /**
   * Perform time synchronization via Socket.IO
   */
  syncViaSocket(socket: any): Promise<TimeSyncStatus> {
    return new Promise((resolve) => {
      const clientSendTime = Date.now();
      
      const handleResponse = (data: TimeSyncData) => {
        const clientReceiveTime = Date.now();
        const roundTripTime = clientReceiveTime - clientSendTime;
        
        // Calculate estimated server time accounting for network delay
        const estimatedServerTime = data.serverTime + (roundTripTime / 2);
        
        // Update offset
        this.offset = estimatedServerTime - clientReceiveTime;
        this.lastSyncTime = Date.now();

        const status = this.getStatus();
        this.notifyListeners(status);

        console.log(`Socket time sync completed. Offset: ${this.offset}ms, RTT: ${roundTripTime}ms`);
        
        // Clean up listener
        socket.off('time_sync_response', handleResponse);
        resolve(status);
      };

      // Set up response listener
      socket.on('time_sync_response', handleResponse);
      
      // Send sync request
      socket.emit('time_sync_request', clientSendTime);

      // Timeout after 5 seconds
      setTimeout(() => {
        socket.off('time_sync_response', handleResponse);
        const status: TimeSyncStatus = {
          synchronized: false,
          offset: this.offset,
          lastSync: this.lastSyncTime,
          error: 'Socket sync timeout',
        };
        this.notifyListeners(status);
        resolve(status);
      }, 5000);
    });
  }

  /**
   * Start periodic time synchronization
   */
  private startPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      this.syncWithServer().catch(console.error);
    }, this.SYNC_INTERVAL);

    // Perform initial sync
    this.syncWithServer().catch(console.error);
  }

  /**
   * Stop periodic synchronization
   */
  stopSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * Add a listener for sync status changes
   */
  addListener(listener: (status: TimeSyncStatus) => void): void {
    this.listeners.push(listener);
  }

  /**
   * Remove a listener
   */
  removeListener(listener: (status: TimeSyncStatus) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Notify all listeners of status changes
   */
  private notifyListeners(status: TimeSyncStatus): void {
    this.listeners.forEach(listener => {
      try {
        listener(status);
      } catch (error) {
        console.error('Error in time sync listener:', error);
      }
    });
  }

  /**
   * Format time remaining with synchronized time
   */
  formatTimeRemaining(endTime: Date | number): string {
    const endTimeMs = typeof endTime === 'number' ? endTime : endTime.getTime();
    const now = this.getServerTime();
    const diff = endTimeMs - now;

    if (diff <= 0) {
      return 'Ended';
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Get minutes remaining with synchronized time
   */
  getMinutesRemaining(endTime: Date | number): number {
    const endTimeMs = typeof endTime === 'number' ? endTime : endTime.getTime();
    const now = this.getServerTime();
    const diff = endTimeMs - now;
    return Math.max(0, Math.floor(diff / (1000 * 60)));
  }
}

// Export singleton instance
export const timeSyncService = new TimeSyncService();
export default timeSyncService;
