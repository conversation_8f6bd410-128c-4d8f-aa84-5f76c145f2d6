import { LiveReel, Bid, BidHistory } from "./types";

export const liveReels: LiveReel[] = [
  {
    id: "fe0",
    title: "Business Class Upgrade Auction",
    thumbnailUrl:
      "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2F22f7dbd67f1742e6b58dbbbf264c26b6",
    videoUrl: "https://cdn.builder.io/o/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2F63b75c00decf45c780e74d61add18845%2Fcompressed?apiKey=ca8d63b380b14e2ba5335ff8fdb3c487&token=63b75c00decf45c780e74d61add18845&alt=media&optimized=true",
    product: {
      id: "pfe1",
      name: "Business Class Upgrade",
      description:
        "Upgrade your economy seat to business class with priority boarding, premium meals, and extra legroom.",
      category: "Aviation Exclusives",
      startingPrice: 100,
      currentBid: 185,
      imageUrl:
        "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2F22f7dbd67f1742e6b58dbbbf264c26b6",
      condition: "Available",
      brand: "Airlines Premium",
    },
    auctioneer: {
      name: "Captain <PERSON>",
      avatar:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
      rating: 4.8,
    },
    viewerCount: 892,
    endTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 23,
  },
  {
    id: "fe1",
    title: "Taylor Swift Concert Tickets",
    thumbnailUrl:
      "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2Fd2fa64c336db475e8083af6424f73eed",
    videoUrl: "https://cdn.builder.io/o/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2F83db6890c92444e6981adf19796ad549?alt=media&token=22ad5da7-0846-452a-acb9-3c6cfccdebaa&apiKey=ca8d63b380b14e2ba5335ff8fdb3c487",
    product: {
      id: "pfe1",
      name: "Business Class Upgrade",
      description:
        "Upgrade your economy seat to business class with priority boarding, premium meals, and extra legroom.",
      category: "Experience",
      startingPrice: 100,
      currentBid: 185,
      imageUrl:
        "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2Fd2fa64c336db475e8083af6424f73eed",
      condition: "Available",
      brand: "Airlines Premium",
    },
    auctioneer: {
      name: "Captain James Wilson",
      avatar:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
      rating: 4.8,
    },
    viewerCount: 892,
    endTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 23,
  },
  {
    id: "fe2",
    title: "Labubu",
    thumbnailUrl:
      "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2F00b12bb4990c4f59ab54bffce7e855ae",
    videoUrl: "https://cdn.builder.io/o/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2Fc9db2bc655664a5591104236a4110995?alt=media&token=e87ab4e8-fdfd-42af-a015-c95b7793436b&apiKey=ca8d63b380b14e2ba5335ff8fdb3c487",
    product: {
      id: "p5",
      name: "Chanel Classic Flap Bag",
      description:
        "Timeless Chanel Classic Flap Bag in black quilted leather with gold hardware.",
      category: "Fashion",
      startingPrice: 3500,
      currentBid: 4200,
      imageUrl:
        "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2F00b12bb4990c4f59ab54bffce7e855ae",
      condition: "Very Good",
      brand: "Chanel",
    },
    auctioneer: {
      name: "Isabella Rodriguez",
      avatar:
        "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face",
      rating: 4.6,
    },
    viewerCount: 678,
    endTime: new Date(Date.now() + 1.5 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 31,
  },
  {
    id: "fe2",
    title: "First Class Food Experience",
    thumbnailUrl:
      "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2Fd08560992516478aa8c89fafdd390c69",
    videoUrl: "https://example.com/video-fe2",
    product: {
      id: "pfe2",
      name: "Gourmet Meal in Economy",
      description:
        "Experience first-class dining while in economy class. Michelin-starred chef prepared meals served to your seat.",
      category: "Flight Experience Upgrade",
      startingPrice: 10,
      currentBid: 18,
      imageUrl:
        "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2Fd08560992516478aa8c89fafdd390c69",
      condition: "Fresh Prepared",
      brand: "Sky Cuisine",
    },
    auctioneer: {
      name: "Chef Maria Rodriguez",
      avatar:
        "https://images.unsplash.com/photo-1494790108755-2616b78e4ba1?w=100&h=100&fit=crop&crop=face",
      rating: 4.9,
    },
    viewerCount: 534,
    endTime: new Date(Date.now() + 1.5 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 15,
  },
  {
    id: "fe3",
    title: "VIP Lounge Access Upon Arrival",
    thumbnailUrl:
      "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2F1bc0ceaf4ad342438a4c132f1f99f663",
    videoUrl: "https://example.com/video-fe3",
    product: {
      id: "pfe3",
      name: "First/Business Lounge Access",
      description:
        "Relax in luxury after your flight with complimentary drinks, wifi, and comfortable seating in premium lounges.",
      category: "Flight Experience Upgrade",
      startingPrice: 15,
      currentBid: 28,
      imageUrl:
        "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2F1bc0ceaf4ad342438a4c132f1f99f663",
      condition: "Day Pass",
      brand: "Airport Lounges",
    },
    auctioneer: {
      name: "Sarah Chen",
      avatar:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
      rating: 4.7,
    },
    viewerCount: 421,
    endTime: new Date(Date.now() + 3 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 19,
  },
  {
    id: "fe4",
    title: "Premium Amenity Kit Auction",
    thumbnailUrl:
      "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2Fc6e459ccf46845c29b2aa3c4573f30f2",
    videoUrl: "https://example.com/video-fe4",
    product: {
      id: "pfe4",
      name: "Designer Amenity Kit",
      description:
        "Luxury travel kit with designer toiletries, eye mask, slippers, and premium travel accessories.",
      category: "Flight Experience Upgrade",
      startingPrice: 10,
      currentBid: 16,
      imageUrl:
        "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2Fc6e459ccf46845c29b2aa3c4573f30f2",
      condition: "Brand New",
      brand: "Luxury Travel Co.",
    },
    auctioneer: {
      name: "Isabella Rodriguez",
      avatar:
        "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face",
      rating: 4.6,
    },
    viewerCount: 312,
    endTime: new Date(Date.now() + 45 * 60 * 1000),
    isLive: true,
    bidCount: 12,
  },
  {
    id: "fe5",
    title: "Empty Row Seat Blocking",
    thumbnailUrl:
      "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2F4c7b40f183964f6dbb1dc53f0d222eef",
    videoUrl: "https://example.com/video-fe5",
    product: {
      id: "pfe5",
      name: "Private Row Experience",
      description:
        "Bid to block adjacent seats and enjoy a whole row to yourself. Perfect for sleeping or extra comfort during long flights.",
      category: "Flight Experience Upgrade",
      startingPrice: 20,
      currentBid: 35,
      imageUrl:
        "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2F4c7b40f183964f6dbb1dc53f0d222eef",
      condition: "Seat Block",
      brand: "Comfort Plus",
    },
    auctioneer: {
      name: "David Kim",
      avatar:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      rating: 4.8,
    },
    viewerCount: 678,
    endTime: new Date(Date.now() + 2.5 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 21,
  },
  {
    id: "fe6",
    title: "Meet the Pilot Post-Flight ",
    thumbnailUrl:
      "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2F1a21710e4d1047d6ab460bdd83d10981",
    videoUrl: "https://example.com/video0",
    product: {
      id: "p0",
      name: "Small group flight deck visit ",
      description:
        "Small group flight deck visit .",
      category: "Aviation Exclusives",
      startingPrice: 20,
      currentBid: 50,
      imageUrl:
        "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2F1a21710e4d1047d6ab460bdd83d10981",
      condition: "Premium Package",
      brand: "Aviation",
    },
    auctioneer: {
      name: "Elena Martinez",
      avatar:
        "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=100&h=100&fit=crop&crop=face",
      rating: 4.9,
    },
    viewerCount: 1856,
    endTime: new Date(Date.now() + 4 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 67,
  },
  {
    id: "fe7",
    title: "Flight Crew Meet & Snap",
    thumbnailUrl:
      "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2Fab350a273e1c43488eee534088fea278",
    videoUrl: "https://example.com/video1",
    product: {
      id: "p1",
      name: "Exclusive chance for a selfie with your in-flight heroes",
      description:
        "Exclusive chance for a selfie with your in-flight heroes.",
      category: "Aviation Exclusives",
      startingPrice: 25,
      currentBid: 30,
      imageUrl:
        "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2Fab350a273e1c43488eee534088fea278",
      condition: "Excellent",
      brand: "Rolex",
    },
    auctioneer: {
      name: "Marcus Webb",
      avatar:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      rating: 4.9,
    },
    viewerCount: 1247,
    endTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 43,
  },
  {
    id: "fe8",
    title: "VIP Tour of Airline Operations",
    thumbnailUrl:
      "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2Fc1b0498edc194fafa7eaa8ece268382e",
    videoUrl: "https://example.com/video2",
    product: {
      id: "p2",
      name: "Go behind the scenes of the skies.",
      description:
        "Ever wondered what it takes to get a flight off the ground? Win an exclusive, guided tour of the airline’s operations center — where routes are planned, flights are monitored in real time, and crews are coordinated across the globe. Meet the pros who keep everything running smoothly and experience the heart of aviation from the inside..",
      category: "Aviation Exclusives",
      startingPrice: 285,
      currentBid: 447,
      imageUrl:
        "https://cdn.builder.io/api/v1/image/assets%2Fca8d63b380b14e2ba5335ff8fdb3c487%2Fc1b0498edc194fafa7eaa8ece268382e",
      condition: "Excellent",
      brand: "LEGO",
    },
    auctioneer: {
      name: "Sarah Chen",
      avatar:
        "https://images.unsplash.com/photo-1494790108755-2616b78e4ba1?w=100&h=100&fit=crop&crop=face",
      rating: 4.7,
    },
    viewerCount: 324,
    endTime: new Date(Date.now() + 45 * 60 * 1000),
    isLive: true,
    bidCount: 18,
  },
  {
    id: "3",
    title: "Vintage Cowboy Hat Collection",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1529958030586-3aae4ca485ff?w=400&h=600&fit=crop",
    videoUrl: "https://example.com/video3",
    product: {
      id: "p3",
      name: "Authentic Western Hat",
      description:
        "Genuine leather cowboy hat from the 1960s. Rare collectible piece in mint condition.",
      category: "Fashion",
      startingPrice: 150,
      currentBid: 285,
      imageUrl:
        "https://images.unsplash.com/photo-1529958030586-3aae4ca485ff?w=600&h=600&fit=crop",
      condition: "Mint",
      brand: "Stetson",
    },
    auctioneer: {
      name: "Jake Morrison",
      avatar:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
      rating: 4.8,
    },
    viewerCount: 567,
    endTime: new Date(Date.now() + 3 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 22,
  },
  {
    id: "4",
    title: "Patek Philippe Grand Complication",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=400&h=600&fit=crop",
    videoUrl: "https://example.com/video4",
    product: {
      id: "p4",
      name: "Patek Philippe Nautilus",
      description:
        "Extremely rare Patek Philippe Nautilus in rose gold. Investment grade timepiece.",
      category: "Watches",
      startingPrice: 85000,
      currentBid: 127500,
      imageUrl:
        "https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=600&h=600&fit=crop",
      condition: "Like New",
      brand: "Patek Philippe",
    },
    auctioneer: {
      name: "Victoria Sterling",
      avatar:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
      rating: 5.0,
    },
    viewerCount: 2891,
    endTime: new Date(Date.now() + 6 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 87,
  },
  {
    id: "5",
    title: "Designer Handbag Showcase",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=600&fit=crop",
    videoUrl: "https://example.com/video5",
    product: {
      id: "p5",
      name: "Chanel Classic Flap Bag",
      description:
        "Timeless Chanel Classic Flap Bag in black quilted leather with gold hardware.",
      category: "Fashion",
      startingPrice: 3500,
      currentBid: 4200,
      imageUrl:
        "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=600&h=600&fit=crop",
      condition: "Very Good",
      brand: "Chanel",
    },
    auctioneer: {
      name: "Isabella Rodriguez",
      avatar:
        "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face",
      rating: 4.6,
    },
    viewerCount: 678,
    endTime: new Date(Date.now() + 1.5 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 31,
  },
  {
    id: "taylor-swift",
    title: "Taylor Swift Concert Tickets Auction",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=600&fit=crop",
    videoUrl: "https://example.com/video-taylor",
    product: {
      id: "taylor-swift",
      name: "Taylor Swift Concert Tickets",
      description:
        "Exclusive VIP tickets to Taylor Swift's upcoming concert with backstage access and meet & greet.",
      category: "Elite Experiences",
      startingPrice: 500,
      currentBid: 850,
      imageUrl:
        "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=600&h=600&fit=crop",
      condition: "VIP Package",
      brand: "Taylor Swift",
    },
    auctioneer: {
      name: "Sarah Williams",
      avatar:
        "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
      rating: 4.9,
    },
    viewerCount: 2847,
    endTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 42,
  },
  {
    id: "disney-tickets",
    title: "Walt Disney World VIP Experience",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=600&fit=crop",
    videoUrl: "https://example.com/video-disney",
    product: {
      id: "disney-tickets",
      name: "Walt Disney Tickets",
      description:
        "Premium Disney World tickets with fast pass access and character dining reservations.",
      category: "Elite Experiences",
      startingPrice: 200,
      currentBid: 320,
      imageUrl:
        "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=600&h=600&fit=crop",
      condition: "Premium Package",
      brand: "Disney",
    },
    auctioneer: {
      name: "Mike Johnson",
      avatar:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      rating: 4.7,
    },
    viewerCount: 1523,
    endTime: new Date(Date.now() + 3 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 18,
  },
  {
    id: "dg-sunglasses",
    title: "D&G Designer Sunglasses Collection",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1511499767150-a48a237f0083?w=400&h=600&fit=crop",
    videoUrl: "https://example.com/video-dg",
    product: {
      id: "dg-sunglasses",
      name: "D&G Sunglasses",
      description:
        "Limited edition Dolce & Gabbana sunglasses with crystal embellishments and gold frames.",
      category: "Luxury Accessories",
      startingPrice: 300,
      currentBid: 450,
      imageUrl:
        "https://images.unsplash.com/photo-1511499767150-a48a237f0083?w=600&h=600&fit=crop",
      condition: "Brand New",
      brand: "Dolce & Gabbana",
    },
    auctioneer: {
      name: "Sophia Chen",
      avatar:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
      rating: 4.8,
    },
    viewerCount: 892,
    endTime: new Date(Date.now() + 1.8 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 12,
  },
  {
    id: "labubu",
    title: "Rare Labubu Collectible Figure",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=600&fit=crop",
    videoUrl: "https://example.com/video-labubu",
    product: {
      id: "labubu",
      name: "Labubu",
      description:
        "Ultra-rare Labubu collectible figure from the limited anniversary series.",
      category: "Collectibles & Pop Culture",
      startingPrice: 80,
      currentBid: 125,
      imageUrl:
        "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=600&fit=crop",
      condition: "Mint in Box",
      brand: "Pop Mart",
    },
    auctioneer: {
      name: "Kevin Park",
      avatar:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
      rating: 4.6,
    },
    viewerCount: 1247,
    endTime: new Date(Date.now() + 2.5 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 28,
  },
  {
    id: "pokemon-cards",
    title: "Limited Edition Pokemon Cards",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=400&h=600&fit=crop",
    videoUrl: "https://example.com/video-pokemon",
    product: {
      id: "pokemon-cards",
      name: "Limited Edition Pokemon Cards",
      description:
        "First edition holographic Pokemon cards from the original base set, professionally graded.",
      category: "Collectibles & Pop Culture",
      startingPrice: 2000,
      currentBid: 2800,
      imageUrl:
        "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=600&h=600&fit=crop",
      condition: "PSA 10 Mint",
      brand: "Pokemon",
    },
    auctioneer: {
      name: "David Martinez",
      avatar:
        "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face",
      rating: 4.9,
    },
    viewerCount: 3421,
    endTime: new Date(Date.now() + 1.2 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 35,
  },
  {
    id: "first-class-upgrade",
    title: "First Class Flight Upgrade",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=400&h=600&fit=crop",
    videoUrl: "https://example.com/video-firstclass",
    product: {
      id: "first-class-upgrade",
      name: "First Class Upgrades",
      description:
        "Luxury first-class upgrade vouchers valid on international flights with premium amenities.",
      category: "Sky Luxe",
      startingPrice: 800,
      currentBid: 1200,
      imageUrl:
        "https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=600&h=600&fit=crop",
      condition: "Premium Voucher",
      brand: "Sky Luxe",
    },
    auctioneer: {
      name: "Amanda Foster",
      avatar:
        "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face",
      rating: 4.8,
    },
    viewerCount: 1876,
    endTime: new Date(Date.now() + 3.5 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 22,
  },
  {
    id: "beats-earphones",
    title: "Beats Wireless Earphones",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=600&fit=crop",
    videoUrl: "https://example.com/video-beats",
    product: {
      id: "beats-earphones",
      name: "Beats Wireless Earphones",
      description:
        "Latest Beats Studio Pro wireless earphones with active noise cancellation and premium sound.",
      category: "Stylish Sound",
      startingPrice: 120,
      currentBid: 180,
      imageUrl:
        "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&h=600&fit=crop",
      condition: "Brand New",
      brand: "Beats",
    },
    auctioneer: {
      name: "Ryan Thompson",
      avatar:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      rating: 4.7,
    },
    viewerCount: 945,
    endTime: new Date(Date.now() + 2.8 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 15,
  },
  {
    id: "bulgari-wonders",
    title: "Bulgari Unexpected Wonders",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=600&fit=crop",
    videoUrl: "https://example.com/video-bulgari",
    product: {
      id: "bulgari-wonders",
      name: "Bulgari Unexpected Wonders",
      description:
        "Exquisite Bulgari jewelry piece from the Unexpected Wonders collection with precious stones.",
      category: "Luxury Accessories",
      startingPrice: 2500,
      currentBid: 3500,
      imageUrl:
        "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=600&h=600&fit=crop",
      condition: "Brand New",
      brand: "Bulgari",
    },
    auctioneer: {
      name: "Victoria Sterling",
      avatar:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
      rating: 5.0,
    },
    viewerCount: 1654,
    endTime: new Date(Date.now() + 4 * 60 * 60 * 1000),
    isLive: true,
    bidCount: 8,
  },
];

export const bidHistories: BidHistory[] = [
  {
    reelId: "fe0",
    bids: [
      {
        id: "b_fe0_1",
        reelId: "fe0",
        amount: 195,
        bidder: {
          name: "Sarah Wilson",
          avatar:
            "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
          flightNumber: "AA123",
        },
        timestamp: new Date(Date.now() - 2 * 60 * 1000),
      },
      {
        id: "b_fe0_2",
        reelId: "fe0",
        amount: 185,
        bidder: {
          name: "John Smith",
          avatar:
            "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
          flightNumber: "BA456",
        },
        timestamp: new Date(Date.now() - 8 * 60 * 1000),
      },
      {
        id: "b_fe0_3",
        reelId: "fe0",
        amount: 175,
        bidder: {
          name: "Lisa Chen",
          avatar:
            "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
        },
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
      },
    ],
  },
  {
    reelId: "1",
    bids: [
      {
        id: "b1",
        reelId: "1",
        amount: 12750,
        bidder: {
          name: "Alex Johnson",
          avatar:
            "https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=100&h=100&fit=crop&crop=face",
          flightNumber: "AA123",
        },
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
      },
      {
        id: "b2",
        reelId: "1",
        amount: 12500,
        bidder: {
          name: "Emma Davis",
          avatar:
            "https://images.unsplash.com/photo-1554151228-14d9def656e4?w=100&h=100&fit=crop&crop=face",
          flightNumber: "BA456",
        },
        timestamp: new Date(Date.now() - 12 * 60 * 1000),
      },
      {
        id: "b3",
        reelId: "1",
        amount: 12000,
        bidder: {
          name: "Michael Brown",
          avatar:
            "https://images.unsplash.com/photo-1633332755192-727a05c4013d?w=100&h=100&fit=crop&crop=face",
          flightNumber: "UA789",
        },
        timestamp: new Date(Date.now() - 18 * 60 * 1000),
      },
    ],
  },
];

export const categories = [
  { id: "experience", name: "Experience", icon: "🛫" },
  { id: "aviation", name: "Aviation Exclusives", icon: "💎" },
  { id: "travel", name: "Travel", icon: "✈️" },
  { id: "watches", name: "Watches", icon: "⌚" },
  { id: "fashion", name: "Fashion", icon: "👗" },
  { id: "toys", name: "Toys", icon: "🧸" },
  { id: "collectibles", name: "Collectibles", icon: "🎨" },
  { id: "electronics", name: "Electronics", icon: "📱" },
  { id: "elite-experiences", name: "Elite Experiences", icon: "🌟" },
  { id: "luxury-accessories", name: "Luxury Accessories", icon: "💎" },
  { id: "collectibles-pop", name: "Collectibles & Pop Culture", icon: "🎭" },
  { id: "sky-luxe", name: "Sky Luxe", icon: "✈️" },
  { id: "stylish-sound", name: "Stylish Sound", icon: "🎵" },
];
