import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { timeSyncService, TimeSyncStatus } from "@/lib/timeSync";
import useSocket from "@/hooks/useSocket";
import { Timer, RefreshCw, Wifi, WifiOff, Clock } from "lucide-react";
import { cn } from "@/lib/utils";

interface TimeSyncStatusProps {
  className?: string;
}

export function TimeSyncStatusComponent({ className }: TimeSyncStatusProps) {
  const [status, setStatus] = useState<TimeSyncStatus>(timeSyncService.getStatus());
  const [currentTime, setCurrentTime] = useState({
    local: Date.now(),
    server: timeSyncService.getServerTime(),
  });
  const { isConnected, performTimeSync } = useSocket();

  // Update status when time sync changes
  useEffect(() => {
    const handleStatusUpdate = (newStatus: TimeSyncStatus) => {
      setStatus(newStatus);
    };

    timeSyncService.addListener(handleStatusUpdate);
    return () => timeSyncService.removeListener(handleStatusUpdate);
  }, []);

  // Update current time display every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime({
        local: Date.now(),
        server: timeSyncService.getServerTime(),
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleManualSync = () => {
    if (isConnected) {
      performTimeSync();
    } else {
      timeSyncService.syncWithServer();
    }
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getTimeDifference = () => {
    return Math.abs(currentTime.server - currentTime.local);
  };

  return (
    <Card className={cn("w-full max-w-md", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Timer className="w-5 h-5" />
            Time Synchronization
          </div>
          <div className="flex items-center gap-2">
            <Badge
              variant={status.synchronized ? "default" : "secondary"}
              className={cn(
                status.synchronized
                  ? "bg-green-100 text-green-800 border-green-200"
                  : "bg-yellow-100 text-yellow-800 border-yellow-200"
              )}
            >
              {status.synchronized ? "Synced" : "Not Synced"}
            </Badge>
            <div className={cn(
              "flex items-center gap-1 px-2 py-1 rounded-full text-xs",
              isConnected
                ? "bg-green-100 text-green-700"
                : "bg-red-100 text-red-700"
            )}>
              {isConnected ? (
                <>
                  <Wifi className="w-3 h-3" />
                  Connected
                </>
              ) : (
                <>
                  <WifiOff className="w-3 h-3" />
                  Offline
                </>
              )}
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Time Display */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-xs text-gray-500 mb-1">Local Time</div>
            <div className="font-mono text-sm">{formatTime(currentTime.local)}</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-xs text-blue-600 mb-1">Server Time</div>
            <div className="font-mono text-sm text-blue-700">
              {formatTime(currentTime.server)}
            </div>
          </div>
        </div>

        {/* Sync Information */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Time Offset:</span>
            <span className={cn(
              "font-mono",
              Math.abs(status.offset) > 1000 ? "text-red-600" : "text-green-600"
            )}>
              {status.offset > 0 ? '+' : ''}{status.offset}ms
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Time Difference:</span>
            <span className={cn(
              "font-mono",
              getTimeDifference() > 1000 ? "text-red-600" : "text-green-600"
            )}>
              {getTimeDifference()}ms
            </span>
          </div>

          {status.lastSync > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Last Sync:</span>
              <span className="text-gray-800">
                {new Date(status.lastSync).toLocaleTimeString()}
              </span>
            </div>
          )}

          {status.error && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
              Error: {status.error}
            </div>
          )}
        </div>

        {/* Manual Sync Button */}
        <Button
          onClick={handleManualSync}
          variant="outline"
          className="w-full"
          disabled={!isConnected && !navigator.onLine}
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          {isConnected ? "Sync via Socket" : "Sync via HTTP"}
        </Button>

        {/* Countdown Example */}
        <div className="border-t pt-4">
          <div className="text-sm text-gray-600 mb-2">Example: Auction ending in 5 minutes</div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="p-2 bg-gray-50 rounded">
              <div className="text-gray-500">Local Countdown:</div>
              <div className="font-mono">
                {timeSyncService.formatTimeRemaining(Date.now() + 5 * 60 * 1000)}
              </div>
            </div>
            <div className="p-2 bg-blue-50 rounded">
              <div className="text-blue-600">Synced Countdown:</div>
              <div className="font-mono text-blue-700">
                {timeSyncService.formatTimeRemaining(timeSyncService.getServerTime() + 5 * 60 * 1000)}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
